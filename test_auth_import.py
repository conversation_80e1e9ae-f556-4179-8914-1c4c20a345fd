#!/usr/bin/env python3
"""Test script to check if auth module can be imported."""

try:
    from services.api.routes import auth
    print("✅ Auth module imported successfully")
    
    # Test if we can create the app
    from services.api.main import create_app
    app = create_app()
    print("✅ FastAPI app created successfully")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
