#!/usr/bin/env python3
"""Minimal server for testing auth endpoint only."""

import os
import time
from datetime import datetime, timedelta
from typing import List, Optional

import jwt
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field

# Mock settings for testing
class MockSettings:
    environment = "development"
    
    class Security:
        jwt_private_key = None
        jwt_algorithm = "RS256"
        jwt_audience = None
        jwt_issuer = None
    
    security = Security()

settings = MockSettings()

app = FastAPI(title="Auth Test Server")

class TokenRequest(BaseModel):
    """Request model for token generation."""
    username: str = Field(..., description="Username for token generation")
    roles: List[str] = Field(default=["service_extract"], description="List of roles for the user")
    expiry_hours: Optional[int] = Field(default=24, description="Token expiry time in hours")


class TokenResponse(BaseModel):
    """Response model for token generation."""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiry time in seconds")
    expires_at: str = Field(..., description="Token expiration timestamp")


@app.post("/auth/token", response_model=TokenResponse)
async def generate_token(request: TokenRequest):
    """Generate a JWT token for API authentication."""
    
    try:
        # Calculate expiry time
        expiry_seconds = request.expiry_hours * 3600
        issued_at = int(time.time())
        expires_at = issued_at + expiry_seconds
        expires_at_iso = datetime.fromtimestamp(expires_at).isoformat() + "Z"
        
        # Create JWT payload
        payload = {
            "sub": request.username,
            "roles": request.roles,
            "iat": issued_at,
            "exp": expires_at,
            "iss": "wedogood-dev",
            "aud": "wedogood-api"
        }
        
        # Development mode: Use HS256 with simple secret
        jwt_secret = os.getenv("JWT_SECRET", "development-secret-key-not-for-production")
        algorithm = "HS256"
        
        token = jwt.encode(payload, jwt_secret, algorithm=algorithm)
        
        return TokenResponse(
            access_token=token,
            token_type="bearer",
            expires_in=expiry_seconds,
            expires_at=expires_at_iso
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Token generation failed: {str(e)}"
        )


@app.get("/health/live")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "auth-test"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
