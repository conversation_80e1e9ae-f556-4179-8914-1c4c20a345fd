#!/usr/bin/env python3
"""Debug script to identify server startup issues."""

import sys
import traceback
import os

def test_imports():
    """Test all imports step by step."""
    print("🔍 Testing imports...")
    
    try:
        print("  ✅ Basic imports...")
        import fastapi
        import uvicorn
        print(f"     FastAPI version: {fastapi.__version__}")
        print(f"     Uvicorn version: {uvicorn.__version__}")
        
        print("  ✅ Common modules...")
        from common.settings import settings
        from common.logging import configure_logging
        
        print("  ✅ Auth route...")
        from services.api.routes import auth
        
        print("  ✅ Health route...")
        from services.api.routes import health
        
        print("  ✅ Middleware...")
        from services.api.middleware import JWTMiddleware
        
        print("  ✅ Main app...")
        from services.api.main import create_app
        
        print("  ✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"  ❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """Test app creation."""
    print("\n🏗️  Testing app creation...")
    
    try:
        from services.api.main import create_app
        app = create_app()
        print("  ✅ App created successfully!")
        return app
        
    except Exception as e:
        print(f"  ❌ App creation failed: {e}")
        traceback.print_exc()
        return None

def test_environment():
    """Test environment variables."""
    print("\n🌍 Testing environment...")
    
    env_vars = [
        "SUPABASE_URL",
        "SUPABASE_KEY", 
        "QDRANT_URL",
        "QDRANT_API_KEY",
        "LINKEDIN_EMAIL",
        "LINKEDIN_PASSWORD"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"  ✅ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"  ⚠️  {var}: Not set")

def main():
    """Main debug function."""
    print("WeDoGood API Server Debug")
    print("=" * 50)
    
    # Test environment
    test_environment()
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed. Cannot proceed.")
        return 1
    
    # Test app creation
    app = test_app_creation()
    if not app:
        print("\n❌ App creation failed. Cannot proceed.")
        return 1
    
    print("\n✅ All tests passed! Server should be able to start.")
    
    # Try to start server programmatically
    print("\n🚀 Attempting to start server...")
    try:
        import uvicorn
        uvicorn.run(app, host="127.0.0.1", port=8002, log_level="info")
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
