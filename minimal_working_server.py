#!/usr/bin/env python3
"""Minimal working server with just auth endpoint."""

import os
import time
from datetime import datetime
from typing import List, Optional

import jwt
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field

app = FastAPI(title="WeDoGood Auth API", version="1.0.0")

class TokenRequest(BaseModel):
    username: str = Field(..., description="Username for token generation")
    roles: List[str] = Field(default=["service_extract"], description="List of roles for the user")
    expiry_hours: Optional[int] = Field(default=24, description="Token expiry time in hours")

class TokenResponse(BaseModel):
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiry time in seconds")
    expires_at: str = Field(..., description="Token expiration timestamp")

@app.post("/auth/token", response_model=TokenResponse)
async def generate_token(request: TokenRequest):
    """Generate a JWT token for API authentication."""
    
    try:
        # Calculate expiry time
        expiry_seconds = request.expiry_hours * 3600
        issued_at = int(time.time())
        expires_at = issued_at + expiry_seconds
        expires_at_iso = datetime.fromtimestamp(expires_at).isoformat() + "Z"
        
        # Create JWT payload
        payload = {
            "sub": request.username,
            "roles": request.roles,
            "iat": issued_at,
            "exp": expires_at,
            "iss": "wedogood-dev",
            "aud": "wedogood-api"
        }
        
        # Development mode: Use HS256 with simple secret
        jwt_secret = os.getenv("JWT_SECRET", "development-secret-key-not-for-production")
        algorithm = "HS256"
        
        token = jwt.encode(payload, jwt_secret, algorithm=algorithm)
        
        return TokenResponse(
            access_token=token,
            token_type="bearer",
            expires_in=expiry_seconds,
            expires_at=expires_at_iso
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Token generation failed: {str(e)}"
        )

@app.get("/health/live")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "wedogood-auth"}

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "WeDoGood Auth API", "version": "1.0.0"}

if __name__ == "__main__":
    import uvicorn
    print("Starting minimal WeDoGood Auth API server...")
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
