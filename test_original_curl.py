#!/usr/bin/env python3
"""Test the original curl command that was failing."""

import subprocess
import time
import requests

def test_with_requests():
    """Test with Python requests."""
    print("Testing with Python requests...")
    
    url = "http://localhost:8000/auth/token"
    payload = {
        "username": "service_extract",
        "roles": ["service_extract"]
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_with_curl():
    """Test with curl command."""
    print("Testing with curl...")
    
    # The original failing command
    cmd = [
        "curl", "--location", "http://localhost:8000/auth/token",
        "--header", "Content-Type: application/json",
        "--data", '{"username": "service_extract", "roles": ["service_extract"]}'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        print(f"Return code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        print(f"Stderr: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Main test function."""
    print("Testing original failing curl command...")
    print("=" * 50)
    
    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(3)
    
    # Test with requests first
    if test_with_requests():
        print("✅ Python requests test passed")
    else:
        print("❌ Python requests test failed")
    
    print()
    
    # Test with curl
    if test_with_curl():
        print("✅ Curl test passed")
    else:
        print("❌ Curl test failed")

if __name__ == "__main__":
    main()
