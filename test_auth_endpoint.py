#!/usr/bin/env python3
"""Test script to verify auth endpoint works."""

import requests
import json

def test_auth_endpoint():
    """Test the auth token endpoint."""
    
    url = "http://localhost:8000/auth/token"
    payload = {
        "username": "service_extract",
        "roles": ["service_extract"]
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            print("✅ Token generated successfully!")
            print(f"Token: {token_data['access_token'][:50]}...")
            print(f"Expires in: {token_data['expires_in']} seconds")
            return token_data['access_token']
        else:
            print("❌ Token generation failed")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    test_auth_endpoint()
