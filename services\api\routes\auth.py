"""
Authentication routes for JWT token generation.

This module provides endpoints for generating JWT tokens for API authentication.
Supports both development and production token generation modes.
"""

import os
import time
from datetime import datetime, timedelta
from typing import List, Optional

import jwt
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from structlog import get_logger

from common.settings import settings

logger = get_logger(__name__)

router = APIRouter()


class TokenRequest(BaseModel):
    """Request model for token generation."""
    username: str = Field(..., description="Username for token generation")
    roles: List[str] = Field(default=["service_extract"], description="List of roles for the user")
    expiry_hours: Optional[int] = Field(default=24, description="Token expiry time in hours")


class TokenResponse(BaseModel):
    """Response model for token generation."""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiry time in seconds")
    expires_at: str = Field(..., description="Token expiration timestamp")


@router.post("/token", response_model=TokenResponse)
async def generate_token(request: TokenRequest):
    """
    Generate a JWT token for API authentication.
    
    This endpoint generates JWT tokens for accessing protected API endpoints.
    In development mode, it uses HS256 with a simple secret.
    In production mode, it would use RS256 with proper key management.
    
    Args:
        request: Token generation request with username, roles, and expiry
        
    Returns:
        TokenResponse: Generated JWT token with metadata
        
    Raises:
        HTTPException: If token generation fails
    """
    
    logger.info(
        "Token generation request",
        username=request.username,
        roles=request.roles,
        expiry_hours=request.expiry_hours
    )
    
    try:
        # Calculate expiry time
        expiry_seconds = request.expiry_hours * 3600
        issued_at = int(time.time())
        expires_at = issued_at + expiry_seconds
        expires_at_iso = datetime.fromtimestamp(expires_at).isoformat() + "Z"
        
        # Create JWT payload
        payload = {
            "sub": request.username,
            "roles": request.roles,
            "iat": issued_at,
            "exp": expires_at,
            "iss": "wedogood-dev",
            "aud": "wedogood-api"
        }
        
        # Generate token based on environment
        if settings.environment in ["development", "dev", "local"]:
            # Development mode: Use HS256 with simple secret
            jwt_secret = os.getenv("JWT_SECRET", "development-secret-key-not-for-production")
            algorithm = "HS256"
            
            token = jwt.encode(payload, jwt_secret, algorithm=algorithm)
            
            logger.info(
                "Development JWT token generated",
                username=request.username,
                roles=request.roles,
                expires_at=expires_at_iso
            )
            
        else:
            # Production mode: Use RS256 with private key
            if not settings.security.jwt_private_key:
                logger.error("JWT private key not configured for production")
                raise HTTPException(
                    status_code=500,
                    detail="JWT private key not configured"
                )
            
            algorithm = settings.security.jwt_algorithm
            token = jwt.encode(
                payload,
                settings.security.jwt_private_key,
                algorithm=algorithm
            )
            
            logger.info(
                "Production JWT token generated",
                username=request.username,
                roles=request.roles,
                expires_at=expires_at_iso
            )
        
        return TokenResponse(
            access_token=token,
            token_type="bearer",
            expires_in=expiry_seconds,
            expires_at=expires_at_iso
        )
        
    except Exception as e:
        logger.error(
            "Token generation failed",
            username=request.username,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Token generation failed: {str(e)}"
        )


@router.get("/verify")
async def verify_token_endpoint():
    """
    Verify that the current token is valid.
    
    This is a protected endpoint that requires a valid JWT token.
    It can be used to test token validation.
    
    Returns:
        dict: Token verification status and user information
    """
    # This endpoint will be protected by the JWT middleware
    # If we reach here, the token is valid
    return {
        "status": "valid",
        "message": "Token is valid and authentication successful"
    }
