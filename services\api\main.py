"""
Main FastAPI application for the WeDoGood Skill Extractor API service.

This is the primary application file with full JWT authentication support.

Key Features:
- LinkedIn profile ingestion with browser automation
- Resume processing (both complex and vector-only approaches)
- Health monitoring with database and vector store checks
- Prometheus metrics collection
- CORS support for web applications
- JWT authentication middleware for protected endpoints
- Request logging and error handling
- Background task processing

Authentication:
- Protected endpoints require JWT tokens with appropriate roles
- Public endpoints (health, docs, metrics) are accessible without authentication
- Development mode supports simple token generation for testing
"""

# Load environment variables first, before any other imports
from dotenv import load_dotenv
load_dotenv()

import time
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import uvicorn
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from starlette.middleware.base import BaseHTTPMiddleware

from common.settings import settings
from common.logging import configure_logging, get_logger, set_request_id, generate_request_id
from common.metrics import initialize_metrics, get_request_count, get_request_duration
from services.api.routes import auth, ingest, query, health
from services.api.middleware import JWTMiddleware

logger = get_logger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request logging and metrics collection."""
    
    async def dispatch(self, request: Request, call_next):
        # Generate and set request ID
        request_id = generate_request_id()
        set_request_id(request_id)
        
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Update metrics
            get_request_count().labels(
                method=request.method,
                endpoint=request.url.path,
                status_code=response.status_code
            ).inc()

            get_request_duration().labels(
                method=request.method,
                endpoint=request.url.path
            ).observe(duration)
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Update metrics for errors
            get_request_count().labels(
                method=request.method,
                endpoint=request.url.path,
                status_code=500
            ).inc()
            
            logger.error(f"Request failed: {e}")
            
            # Return error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "request_id": request_id,
                    "message": "An unexpected error occurred"
                },
                headers={"X-Request-ID": request_id}
            )


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager for startup and shutdown tasks."""
    
    # Startup
    logger.info("Starting WeDoGood Skill Extractor API service")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")
    logger.info(f"LinkedIn credentials configured: email={bool(settings.linkedin.email)}, password={bool(settings.linkedin.password)}")
    if settings.linkedin.email:
        logger.info(f"LinkedIn email: {settings.linkedin.email[:10]}...")

    # Initialize metrics
    initialize_metrics()
    logger.info("Prometheus metrics initialized")

    yield
    
    # Shutdown
    logger.info("Shutting down WeDoGood Skill Extractor API service")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    # Configure logging
    configure_logging(
        service_name="api",
        log_level=settings.log_level,
        json_logs=settings.environment == "production"
    )
    
    # Create FastAPI app
    app = FastAPI(
        title="WeDoGood Skill Extractor API",
        description="Backend API for extracting and vectorizing skills from LinkedIn profiles and résumé PDFs for volunteer matching",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add JWT authentication middleware
    # Note: Add this before request logging to ensure authentication happens first
    app.add_middleware(JWTMiddleware)

    # Add request logging middleware
    app.add_middleware(RequestLoggingMiddleware)

    # Include routers
    app.include_router(auth.router, prefix="/auth", tags=["authentication"])
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(ingest.router, prefix="/api/ingest", tags=["ingestion"])
    app.include_router(query.router, prefix="/api", tags=["query"])
    
    # Metrics endpoint
    @app.get("/metrics")
    async def metrics():
        """Prometheus metrics endpoint."""
        return Response(
            content=generate_latest(),
            media_type=CONTENT_TYPE_LATEST
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "wedogood-skill-extractor-api",
            "version": "1.0.0",
            "status": "healthy",
            "environment": settings.environment,
            "docs_url": "/docs",
            "description": "WeDoGood Volunteer Skill Extraction and Matching API",
            "features": [
                "LinkedIn profile ingestion",
                "Health monitoring",
                "Vector similarity search",
                "Prometheus metrics"
            ]
        }
    
    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    uvicorn.run(
        "services.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
