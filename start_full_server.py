#!/usr/bin/env python3
"""Start the full server with better error handling."""

import os
import sys
import traceback

def check_environment():
    """Check required environment variables."""
    print("🌍 Checking environment variables...")
    
    required_vars = {
        "SUPABASE_URL": "Supabase database URL",
        "SUPABASE_KEY": "Supabase API key",
        "QDRANT_URL": "Qdrant vector database URL",
        "QDRANT_API_KEY": "Qdrant API key"
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"  ❌ {var}: {description}")
        else:
            print(f"  ✅ {var}: Set")
    
    if missing_vars:
        print("\n⚠️  Missing environment variables:")
        for var in missing_vars:
            print(var)
        print("\nNote: Server may still work with default/mock values for development.")
    
    return len(missing_vars) == 0

def start_server():
    """Start the full server."""
    print("\n🚀 Starting full WeDoGood API server...")
    
    try:
        # Import and start the server
        import uvicorn
        from services.api.main import create_app
        
        print("  ✅ Creating app...")
        app = create_app()
        
        print("  ✅ Starting server on http://localhost:8000...")
        uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return 1

def main():
    """Main function."""
    print("WeDoGood API Server Startup")
    print("=" * 50)
    
    # Check environment
    env_ok = check_environment()
    
    if not env_ok:
        print("\n⚠️  Some environment variables are missing.")
        print("Continue anyway? (y/n): ", end="")
        response = input().lower().strip()
        if response != 'y':
            print("Exiting...")
            return 1
    
    # Start server
    return start_server()

if __name__ == "__main__":
    sys.exit(main())
