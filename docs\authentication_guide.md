# WeDoGood API Authentication Guide

## Overview

The WeDoGood API uses JWT (JSON Web Token) authentication for secure access to protected endpoints. This guide covers token generation, management, and automatic refresh mechanisms.

## 🔐 Authentication Methods

### 1. Automatic Authentication (Recommended)
The Postman collection includes automatic token management that handles:
- Token generation on first use
- Automatic refresh before expiration
- Collection-level authorization setup

**No manual token management required!**

### 2. Manual Token Generation
For debugging or custom integrations, you can manually generate tokens.

## 📋 Token Details

### Token Properties
- **Type**: Bearer JWT Token
- **Expiration**: 24 hours (86400 seconds)
- **Refresh Buffer**: 5 minutes before expiration
- **Required Role**: `service_extract` for most endpoints

### Token Structure
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 86400,
  "expires_at": "2025-01-08T12:00:00Z"
}
```

## 🚀 Getting Started

### Using Postman Collection

1. **Import Collection**: Import `WeDoGood_API_Collection.postman_collection.json`
2. **Set Base URL**: Update `{{base_url}}` variable if needed (default: `http://localhost:8000`)
3. **Start Using**: Authentication is handled automatically!

### Manual Token Generation

#### Step 1: Generate Token
```bash
curl --location "http://localhost:8000/auth/token" \
  --header "Content-Type: application/json" \
  --data '{
    "username": "service_extract",
    "roles": ["service_extract"]
  }'
```

#### Step 2: Use Token
```bash
curl -X GET "http://localhost:8000/api/query/volunteer/123" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🔄 Automatic Token Management

### How It Works
The Postman collection includes pre-request scripts that:

1. **Check Token Status**: Before each request, checks if token exists and is valid
2. **Auto-Refresh**: If token is missing or expires within 5 minutes, generates a new one
3. **Seamless Integration**: All requests automatically include the current valid token

### Pre-Request Script Logic
```javascript
// Check if token needs refresh
const currentToken = pm.collectionVariables.get('auth_token');
const tokenExpiresAt = pm.collectionVariables.get('token_expires_at');
const now = Date.now();

// Refresh if missing or expiring soon (5 minute buffer)
if (!currentToken || (now + 300000) > parseInt(tokenExpiresAt)) {
    // Generate new token automatically
    // Update collection variables
}
```

## 🛠️ Configuration

### Environment Variables
The collection uses these variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `base_url` | `http://localhost:8000` | API base URL |
| `auth_token` | Auto-generated | Current JWT token |
| `token_expires_at` | Auto-managed | Token expiration timestamp |
| `volunteer_id` | Sample ID | Example volunteer ID for testing |

### Collection-Level Authorization
```json
{
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{auth_token}}",
        "type": "string"
      }
    ]
  }
}
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Authentication Required (401)
**Cause**: Token is missing, expired, or invalid
**Solution**: 
- Use the "Generate Auth Token" request manually
- Check if API server is running
- Verify the base_url is correct

#### 2. Token Generation Fails
**Cause**: API server not responding or auth endpoint unavailable
**Solution**:
- Verify API server is running on correct port
- Check network connectivity
- Ensure auth endpoint exists

#### 3. Automatic Refresh Not Working
**Cause**: Pre-request script errors or collection variables not set
**Solution**:
- Check Postman console for script errors
- Manually run "Generate Auth Token" request
- Verify collection variables are properly set

### Debug Commands

#### Check API Health
```bash
curl http://localhost:8000/health/live
```

#### Manual Token Test
```bash
curl --location "http://localhost:8000/auth/token" \
  --header "Content-Type: application/json" \
  --data '{"username": "service_extract", "roles": ["service_extract"]}'
```

#### Verify Token
```bash
curl -X GET "http://localhost:8000/api/query/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📚 API Endpoints by Authentication

### Public Endpoints (No Auth Required)
- `GET /` - Service information
- `GET /health/*` - Health checks
- `GET /metrics` - Prometheus metrics
- `POST /ingest/linkedin-minimal` - LinkedIn ingestion

### Protected Endpoints (Auth Required)
- `POST /api/ingest/resume` - Resume processing
- `GET /api/query/volunteer/{id}` - Get volunteer profile
- `POST /api/query/match` - Vector similarity search
- `POST /api/query/resumes/search` - Resume semantic search
- `GET /api/query/stats` - Database statistics

## 🔒 Security Best Practices

### Token Security
- Tokens are automatically managed and refreshed
- Never log or expose tokens in plain text
- Use HTTPS in production environments
- Tokens expire automatically for security

### Development vs Production
- **Development**: Use provided test tokens and automatic refresh
- **Production**: Implement proper user authentication and role-based access
- **Testing**: Use collection's automatic token management

## 📖 Additional Resources

- [API Documentation](../README.md)
- [Resume Search Guide](../resume_search_implementation_guide.md)
- [Postman Collection](../WeDoGood_API_Collection.postman_collection.json)

## 🆘 Support

If you encounter authentication issues:
1. Check the troubleshooting section above
2. Verify API server is running and accessible
3. Use manual token generation for debugging
4. Check Postman console for script errors
