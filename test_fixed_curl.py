#!/usr/bin/env python3
"""Test the fixed curl command."""

import subprocess

def test_fixed_curl():
    """Test the corrected curl command."""
    print("Testing the corrected curl command...")
    
    # Fixed command (removed duplicate Content-Type header)
    cmd = [
        "curl", "--location", "http://localhost:8000/auth/token",
        "--header", "Content-Type: application/json",
        "--data", '{"username": "service_extract", "roles": ["service_extract"]}'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        print(f"Return code: {result.returncode}")
        print(f"Response: {result.stdout}")
        
        if result.returncode == 0:
            print("✅ Fixed curl command works!")
            return True
        else:
            print(f"❌ Command failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_fixed_curl()
