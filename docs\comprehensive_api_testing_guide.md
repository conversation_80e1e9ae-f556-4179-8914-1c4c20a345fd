# Comprehensive API Testing Guide

## Overview

This guide provides step-by-step instructions for testing all WeDoGood API endpoints, including troubleshooting common issues and understanding authentication requirements.

## 🔍 Issue Analysis: Your Original API Call

### What Went Wrong

Your original API call:
```bash
curl --location 'http://localhost:8000/api/ingest/resume' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer [TOKEN_MISSING]' \
--data-raw '{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
    "filename": "AbhishekBisht[5y_0m].pdf"
}'
```

**Issues Identified:**
1. ❌ **Missing Bearer Token**: `[TOKEN_MISSING]` is not a valid JWT token
2. ❌ **No Server Running**: The API server was not running on localhost:8000
3. ❌ **No Response Visibility**: You didn't see the actual HTTP response

**Expected Response (with missing token):**
- Status Code: `401 Unauthorized`
- Response Body: `{"error": "Unauthorized", "message": "Missing authentication token"}`

## 🚀 Quick Start Testing

### 1. Start the API Server

```bash
# Navigate to project directory
cd /path/to/WeDoGood/VolunteerDataExtraction

# Start the server
python run.py --reload
```

**Expected Output:**
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process
INFO:     Started server process
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

### 2. Verify Server is Running

```bash
curl http://localhost:8000/health
```

**Expected Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-06T10:00:00Z",
  "components": [...]
}
```

### 3. Generate Test Authentication Token

```bash
# Generate a simple test token for development
python scripts/diagnose_api_issues.py --simple-token
```

**Expected Output:**
```
✅ Simple test token created
🎫 Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 📄 Resume Processing Endpoints

### Original Resume Endpoint (Complex Processing)

**Endpoint:** `POST /api/ingest/resume`

**Features:**
- Structured parsing of resume sections
- Multi-vector embeddings (7 different vectors)
- Complete profile data extraction
- Supabase metadata storage

**Test Command:**
```bash
curl --location 'http://localhost:8000/api/ingest/resume' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_TOKEN_HERE' \
--data-raw '{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
    "filename": "AbhishekBisht[5y_0m].pdf"
}'
```

**Expected Success Response:**
```json
{
  "volunteer_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "message": "Resume processing completed <NAME_EMAIL>",
  "estimated_completion": null
}
```

### Resume Processing with Semantic Search

**Endpoint:** `POST /api/ingest/resume`

**Features:**
- Vector-only processing optimized for semantic search
- Complete text embedding for comprehensive content capture
- Robust handling of varying resume formats
- Optimized for search and matching capabilities

**Test Command:**
```bash
curl --location 'http://localhost:8000/api/ingest/resume' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_TOKEN_HERE' \
--data-raw '{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
    "filename": "AbhishekBisht[5y_0m].pdf"
}'
```

**Expected Success Response:**
```json
{
  "volunteer_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "message": "Resume vector processing completed <NAME_EMAIL>. Processed 3999 characters in 2.01 seconds.",
  "estimated_completion": null
}
```

## Resume Search Endpoints

### Resume Semantic Search

**Endpoint:** `POST /api/query/resumes/search`

**Features:**
- Natural language query support
- Semantic understanding of skills and experience
- Optimized for resume-specific searches
- Automatic authentication handling

**Test Command:**
```bash
curl --location 'http://localhost:8000/api/query/resumes/search' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_TOKEN_HERE' \
--data-raw '{
    "query_text": "Python developer with machine learning experience",
    "top_k": 5,
    "score_threshold": 0.6
}'
```

**Expected Success Response:**
```json
{
  "results": [
    {
      "volunteer_id": "38fa254c-d388-5294-82ab-b06e8af01cf9",
      "score": 0.847,
      "metadata": {
        "email": "<EMAIL>",
        "filename": "AbhishekBisht[5y_0m].pdf",
        "text_length": 3999,
        "processing_approach": "vector_only",
        "search_type": "resume",
        "query_text": "Python developer with machine learning experience"
      }
    }
  ],
  "total_results": 1,
  "query_time_ms": 156.7,
  "vector_field": "combined_vector",
  "vector_dimension": 1536
}
```

### Enhanced Vector Search

**Endpoint:** `POST /api/query/match`

**Features:**
- Support for text queries, vector queries, and reference volunteer searches
- Multi-vector field support
- Enhanced with natural language processing

**Test Command:**
```bash
curl --location 'http://localhost:8000/api/query/match' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_TOKEN_HERE' \
--data-raw '{
    "query_text": "marketing experience with digital campaigns",
    "vector_field": "combined_vector",
    "top_k": 5,
    "score_threshold": 0.6
}'
```

**Expected Success Response:**
```json
{
  "results": [
    {
      "volunteer_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "message": "Resume vector processing completed <NAME_EMAIL>. Processed 3999 characters in 2.22 seconds.",
  "estimated_completion": null
}
```

## 🔗 LinkedIn Processing Endpoint

**Endpoint:** `POST /api/ingest/linkedin`

**Prerequisites:**
- LinkedIn credentials configured
- Azure OpenAI API access

**Test Command:**
```bash
curl --location 'http://localhost:8000/api/ingest/linkedin' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_TOKEN_HERE' \
--data-raw '{
    "url": "https://www.linkedin.com/in/yashkhivasara/",
    "email": "<EMAIL>"
}'
```

## 🔧 Automated Testing Scripts

### Direct Functionality Testing (No Server Required)

```bash
# Test resume processing directly
python scripts/test_resume_endpoints_directly.py

# Test LinkedIn functionality
python scripts/test_linkedin_functionality.py
```

### API Diagnostic Tool

```bash
# Full diagnostic with server running
python scripts/diagnose_api_issues.py

# Generate test token only
python scripts/diagnose_api_issues.py --simple-token

# Setup JWT keys for development
python scripts/diagnose_api_issues.py --setup-keys
```

### Quick Vector-Only Testing

```bash
# Quick test of vector-only processing
python scripts/quick_test_vector_resume.py
```

## 🔐 Authentication Setup

### Development Authentication

For development testing, use the simple token generator:

```bash
python scripts/diagnose_api_issues.py --simple-token
```

This generates a token with:
- Algorithm: HS256 (simple for development)
- Role: `service_extract`
- Expiration: 1 hour

### Production Authentication

For production, configure proper JWT keys:

1. **Generate RSA Key Pair:**
```bash
python scripts/diagnose_api_issues.py --setup-keys
```

2. **Configure Environment:**
```bash
# In .env file
JWT_PUBLIC_KEY=path/to/public.key
JWT_PRIVATE_KEY=path/to/private.key
JWT_ALGORITHM=RS256
```

## 🐛 Common Issues & Solutions

### Issue 1: "Connection refused" or "Failed to connect"

**Cause:** API server not running

**Solution:**
```bash
python run.py --reload
```

### Issue 2: "401 Unauthorized"

**Cause:** Missing or invalid Bearer token

**Solution:**
```bash
# Generate test token
python scripts/diagnose_api_issues.py --simple-token

# Use in curl command
curl -H "Authorization: Bearer YOUR_GENERATED_TOKEN" ...
```

### Issue 3: "403 Forbidden"

**Cause:** Token doesn't have required role

**Solution:** Ensure token includes `"roles": ["service_extract"]`

### Issue 4: Resume processing fails

**Cause:** Various (PDF access, parsing, vector generation)

**Solution:**
```bash
# Test directly to see detailed logs
python scripts/test_resume_endpoints_directly.py
```

### Issue 5: LinkedIn processing fails

**Cause:** Missing Azure OpenAI configuration

**Solution:** Configure in .env:
```bash
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_KEY=your_api_key
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name
LINKEDIN_EMAIL=<EMAIL>
LINKEDIN_PASSWORD=your_password
```

## 📊 Test Results Interpretation

### Resume Processing Success Indicators

**Original Endpoint:**
- Status: 200 OK
- Message contains "Resume processing completed successfully"
- Processing extracts skills, experience, education
- Multiple vectors generated

**Vector-Only Endpoint:**
- Status: 200 OK
- Message contains "Resume vector processing completed successfully"
- Shows character count and processing time
- Single vector generated

### LinkedIn Processing Success Indicators

- Status: 200 OK
- Message: "LinkedIn profile submitted for processing"
- Background task queued
- Volunteer ID generated

## 🎯 Recommendations

### For Your Specific Use Case

1. **Use Vector-Only Endpoint**: `/api/ingest/resume-simple`
   - More robust for varying resume formats
   - Faster processing
   - Better error handling

2. **Authentication**: Use the simple token generator for testing

3. **Monitoring**: Check logs for detailed processing information

### Best Practices

1. **Always check server status** before making API calls
2. **Use proper authentication tokens** with correct roles
3. **Monitor processing logs** for detailed error information
4. **Test with known good files** first (from s3://skill-assessment-test/pdfs/)
5. **Use direct testing scripts** for debugging without HTTP layer

## ✅ Quick Troubleshooting Checklist

### Before Making API Calls

- [ ] API server is running (`curl http://localhost:8000/health`)
- [ ] Environment variables are configured (`.env` file exists)
- [ ] Valid JWT token generated
- [ ] Correct endpoint URL (check `/api/ingest/` prefix)

### If API Call Fails

1. **Check HTTP Status Code:**
   - `401`: Missing/invalid token → Generate new token
   - `403`: Insufficient permissions → Check token roles
   - `404`: Wrong endpoint → Verify URL
   - `422`: Invalid request data → Check JSON format
   - `500`: Server error → Check logs

2. **Check Response Body:**
   - Look for `error` and `message` fields
   - Note `error_type` for specific issue classification

3. **Check Server Logs:**
   - Look for detailed error messages
   - Check processing steps and where they fail

### For Your Original Issue

**Problem:** API call with `[TOKEN_MISSING]` didn't work

**Solution:**
```bash
# 1. Start server
python run.py --reload

# 2. Generate token
python scripts/diagnose_api_issues.py --simple-token

# 3. Use corrected command
curl --location 'http://localhost:8000/api/ingest/resume-simple' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_ACTUAL_TOKEN' \
--data-raw '{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
    "filename": "AbhishekBisht[5y_0m].pdf"
}'
```

## 📝 Next Steps

1. Start the API server: `python run.py --reload`
2. Generate test token: `python scripts/diagnose_api_issues.py --simple-token`
3. Test vector-only endpoint with your resume
4. Monitor logs for any issues
5. Use direct testing scripts for deeper debugging if needed

## 📞 Support

If you continue to have issues:
1. Run the diagnostic script: `python scripts/diagnose_api_issues.py`
2. Check the comprehensive logs from direct testing
3. Verify environment configuration with: `python run.py --check-env`
